import React,{ useContext } from 'react'
import { NotificationContext } from '../../../context/DashboardNotificationsContext/NotificationContext';
import { Snackbar } from '@material-ui/core';
import { Alert } from '@material-ui/lab';

export const NotificationCenter = () => {
	const { notifications, remove } = useContext(NotificationContext);

  return (
	<>
      {notifications.map(({ id, message, type }) => (
        <Snackbar
          key={id}
          open
          autoHideDuration={5000}
          onClose={() => remove(id)}
        >
          <Alert onClose={() => remove(id)} severity={type}>
            {message}
          </Alert>
        </Snackbar>
      ))}
    </>
  )
}
