import React, { useCallback, useState } from 'react'
import { NotificationContext } from './NotificationContext'

export const NotificationProvider = ({ children }) => {
	const [notifications, setNotifications] = useState([]);

	const push = useCallback(({ id, message, type = 'info' }) => {
		setNotifications((nv) => [...nv, { id, message, type }]);
	}, []);

  	const remove = useCallback((id) => {
		setNotifications((nv) => nv.filter(n => n.id !== id));
	}, []);


  return (
	<NotificationContext.Provider value={{ notifications, push, remove }}>
		{children}
	</NotificationContext.Provider>
  )
}
