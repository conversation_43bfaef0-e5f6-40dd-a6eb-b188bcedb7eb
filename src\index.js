import React from "react";
import ReactDOM from "react-dom";
import "./index.css";
import App from "./App";
import UserProvider from "./context/UserProvider";
import { IrrigationProvider } from "./context/IrrigationContext";
import { MulticropProvider } from "./context/MultiCropContext";
import { NotificationProvider } from "./context/DashboardNotificationsContext/NotificationProvider";

ReactDOM.render(
  <UserProvider>
    <MulticropProvider>
      <NotificationProvider>
      <IrrigationProvider>
        {console.log("V2.1.4")}
        <App />
      </IrrigationProvider>
      </NotificationProvider>
    </MulticropProvider>
  </UserProvider>,
  document.getElementById("root")
);
