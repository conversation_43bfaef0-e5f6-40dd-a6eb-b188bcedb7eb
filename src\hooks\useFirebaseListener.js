import { useContext, useEffect } from 'react'
import { NotificationContext } from '../context/DashboardNotificationsContext/NotificationContext';
import { UserContext } from '../context/UserProvider';
import { db } from '../config/firebase';


export const useFirebaseListener = (commandData) => {
	const { usuario } = useContext(UserContext);
	const { push } = useContext(NotificationContext);

	const getMessage = (action,expectedVal,tankName) => {
		if(action === 0 && expectedVal === 2){
			return `El tanque ${tankName} ha terminado el tiempo de espera.`
		}

		const actionText = action === 1 ? "llenado" : (action === 0 ? "vaciado" : "recirculación");
		const expectedValText = expectedVal === 1 ? "iniciado" : "terminado";
		const message = `El tanque ${tankName} ha ${expectedValText} el proceso de ${actionText}.`;
		return message;
	}

	useEffect(() => {
		// Si el objeto es null o no es un objeto, considera que tiene "vacío" o un problema.
		if (commandData === null || typeof commandData !== 'object') {
			console.error("commandData es null, undefined o no es un objeto válido en useFirebaseListener:", commandData);
			return true;
		}
		const addr = `${usuario.username}/infoDevices/${commandData.mac}/${commandData.canid}/fromModule`;
		const docRef = db.collection(addr).doc("configOK")

		const unsubscribe = docRef.onSnapshot(snapshot => {
			if (snapshot.exists) {
				const data = snapshot.data();
				if(data &&
				   data.accion === 244 &&
				   data.act === 'recOK' &&
				   data.kind === commandData.action &&
				   data.val === commandData.expectedVal &&
				   data.outid === commandData.tankId)
				   {
					const id = `${commandData.tankId}-${commandData.action}`;
					const message = getMessage(commandData.action,commandData.expectedVal,commandData.tankName);
					push({
						id,
						message: message,
          				type: 'success'
					})
					if(data.kind === 0 && data.val === 2) {
						const secondId = `${id}-start`
						push({
							secondId,
							message: `El tanque ${commandData.tankName} ha iniciado el proceso de vaciado.`,
          					type: 'info'
						})
					}

				}
			}
		});

		return () => {
			unsubscribe();
		};
	  
	}, [usuario.username,commandData,push])
	
}
